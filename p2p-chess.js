/**
 * P2P Chess Game - PeerJS Implementation
 * Handles peer-to-peer connections, game state synchronization, and communication
 */

class P2PChessManager {
    constructor() {
        this.peer = null;
        this.connection = null;
        this.isHost = false;
        this.isConnected = false;
        this.myPeerId = null;
        this.remotePeerId = null;
        this.roomCode = null;

        // Game state
        this.gameStarted = false;
        this.playerSide = null;
        this.isPlayerTurn = false;

        // UI elements
        this.statusText = document.getElementById('status-text');
        this.lobbyMain = document.getElementById('lobby-main');
        this.roomCreationView = document.getElementById('room-creation-view');
        this.roomCodeElement = document.getElementById('room-code');
        this.roomCodeInput = document.getElementById('room-code-input');
        this.roomStatusMessage = document.getElementById('room-status-message');
        this.singlePlayerLobbyBtn = document.getElementById('single-player-lobby-btn');
        this.createRoomBtn = document.getElementById('create-room-btn');
        this.joinRoomBtn = document.getElementById('join-room-btn');
        this.confirmJoinBtn = document.getElementById('confirm-join-btn');
        this.joinRoomInput = document.getElementById('join-room-input');
        this.copyCodeBtn = document.getElementById('copy-code-btn');
        this.backToLobbyBtn = document.getElementById('back-to-lobby-btn');
        this.connectionProgress = document.getElementById('connection-progress');
        this.connectionMessage = document.getElementById('connection-message');
        this.cancelConnectionBtn = document.getElementById('cancel-connection-btn');
        this.p2pLobby = document.getElementById('p2p-lobby');
        this.gameMode = document.getElementById('game');

        this.initializeEventListeners();
    }

    initializeEventListeners() {
        this.singlePlayerLobbyBtn.addEventListener('click', () => this.switchToSinglePlayer());
        this.createRoomBtn.addEventListener('click', () => this.createRoom());
        this.joinRoomBtn.addEventListener('click', () => this.showJoinInput());
        this.confirmJoinBtn.addEventListener('click', () => this.joinRoom());
        this.copyCodeBtn.addEventListener('click', () => this.copyRoomCode());
        this.backToLobbyBtn.addEventListener('click', () => this.backToLobby());
        this.cancelConnectionBtn.addEventListener('click', () => this.cancelConnection());

        // Enter key in room code input
        this.roomCodeInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.joinRoom();
            }
        });

        // Format room code input to accept letters and numbers
        this.roomCodeInput.addEventListener('input', (e) => {
            e.target.value = e.target.value.replace(/[^A-Z0-9]/g, '').substring(0, 6).toUpperCase();
        });
    }

    async initializePeer() {
        try {
            this.updateStatus('Initializing...', 'connecting');

            // Create peer with random ID
            this.peer = new Peer({
                config: {
                    iceServers: [
                        { urls: 'stun:stun.l.google.com:19302' },
                        { urls: 'stun:stun1.l.google.com:19302' }
                    ]
                }
            });

            this.peer.on('open', (id) => {
                this.myPeerId = id;
                this.updateStatus('Online', 'connected');
                console.log('Peer initialized with ID:', id);
            });

            this.peer.on('connection', (conn) => {
                console.log('Incoming connection from:', conn.peer);
                this.handleIncomingConnection(conn);
            });

            this.peer.on('error', (error) => {
                console.error('Peer error:', error);
                this.updateStatus('Connection error', 'disconnected');
                this.showError('Failed to initialize peer connection: ' + error.message);
            });

            this.peer.on('disconnected', () => {
                console.log('Peer disconnected');
                this.updateStatus('Disconnected', 'disconnected');
            });

        } catch (error) {
            console.error('Failed to initialize peer:', error);
            this.updateStatus('Failed to initialize', 'disconnected');
            this.showError('Failed to initialize peer connection');
        }
    }

    generateRoomCode() {
        // Generate a 6-character room code with letters and numbers like "ELHGVZ"
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < 6; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    createRoom() {
        // Initialize peer if not already done
        if (!this.peer) {
            this.initializePeer();
        }

        this.isHost = true;
        this.roomCode = this.generateRoomCode();

        // Show the room creation view
        this.showRoomCreationView();

        // Create a new peer with the room-specific ID
        this.createRoomPeer();

        console.log('Room created with code:', this.roomCode);
    }

    showRoomCreationView() {
        // Hide the main lobby and show room creation view
        this.lobbyMain.style.display = 'none';
        this.roomCreationView.style.display = 'block';

        // Display the room code
        this.roomCodeElement.textContent = this.roomCode;

        // Show the success message
        this.roomStatusMessage.textContent = 'Game created! Share the code above with your opponent.';
    }

    backToLobby() {
        // Hide room creation view and show main lobby
        this.roomCreationView.style.display = 'none';
        this.lobbyMain.style.display = 'block';

        // Reset room state
        this.roomCode = null;
        this.isHost = false;

        // Close peer connection if exists
        if (this.peer) {
            this.peer.destroy();
            this.peer = null;
        }

        // Re-initialize peer for new connections
        this.initializePeer();
    }

    createRoomPeer(roomPeerId) {
        // Close existing peer
        if (this.peer) {
            this.peer.destroy();
        }

        // Use a predictable room host ID based on room code
        const roomHostId = `chess_room_${this.roomCode}`;

        // Create new peer with room-specific ID
        this.peer = new Peer(roomHostId, {
            config: {
                iceServers: [
                    { urls: 'stun:stun.l.google.com:19302' },
                    { urls: 'stun:stun1.l.google.com:19302' }
                ]
            }
        });

        this.peer.on('open', (id) => {
            this.myPeerId = id;
            console.log('Room peer created with ID:', id);
            this.updateStatus(`Room ${this.roomCode} created`, 'connected');
        });

        this.peer.on('connection', (conn) => {
            console.log('Incoming connection from:', conn.peer);
            this.handleIncomingConnection(conn);
        });

        this.peer.on('error', (error) => {
            console.error('Room peer error:', error);
            if (error.type === 'unavailable-id') {
                // Room code already exists, generate a new one
                this.roomCode = this.generateRoomCode();
                this.roomCodeElement.textContent = this.roomCode;
                this.createRoomPeer(); // Retry with new room code
            } else {
                this.showError('Failed to create room: ' + error.message);
                this.hideConnectionProgress();
            }
        });
    }

    switchToSinglePlayer() {
        // Hide P2P lobby and show single player mode
        this.p2pLobby.style.display = 'none';

        // Show single player mode if it exists
        const singlePlayerMode = document.getElementById('single-player-mode');
        if (singlePlayerMode) {
            singlePlayerMode.style.display = 'block';
        }

        // Also show mode selector to allow switching back
        const modeSelector = document.getElementById('mode-selector');
        if (modeSelector) {
            modeSelector.style.display = 'block';
        }
    }

    showJoinInput() {
        this.joinRoomInput.style.display = 'flex';
        this.roomCodeInput.focus();
    }

    async joinRoom() {
        const roomCode = this.roomCodeInput.value.trim().toUpperCase();
        if (!roomCode) {
            this.showError('Please enter a room code');
            return;
        }

        if (roomCode.length !== 6 || !/^[A-Z0-9]{6}$/.test(roomCode)) {
            this.showError('Room code must be exactly 6 characters (letters and numbers)');
            return;
        }

        this.isHost = false;
        this.roomCode = roomCode;
        this.showConnectionProgress('Connecting to room...');

        try {
            console.log('🎯 Attempting to join room:', roomCode);

            // Initialize peer if not already done or if it's not open
            if (!this.peer || this.peer.disconnected || this.peer.destroyed) {
                console.log('🔧 Initializing peer for joining...');
                await this.initializePeer();

                // Wait for peer to be ready
                await new Promise((resolve, reject) => {
                    if (this.peer.open) {
                        console.log('✅ Peer already open');
                        resolve();
                        return;
                    }

                    const timeout = setTimeout(() => {
                        reject(new Error('Peer initialization timeout'));
                    }, 10000);

                    this.peer.on('open', () => {
                        console.log('✅ Peer opened successfully');
                        clearTimeout(timeout);
                        resolve();
                    });

                    this.peer.on('error', (error) => {
                        console.error('💥 Peer error during initialization:', error);
                        clearTimeout(timeout);
                        reject(error);
                    });
                });
            }

            // Now try to connect to the room
            console.log('🔍 Searching for room host...');
            this.findAndConnectToRoom(roomCode);

        } catch (error) {
            console.error('💥 Failed to join room:', error);
            this.hideConnectionProgress();
            this.showError('Failed to join room: ' + error.message);
        }
    }

    findAndConnectToRoom(roomCode) {
        // Try to connect using the room code pattern
        // We'll attempt to connect to a peer ID that follows our room pattern

        // Since we can't easily discover peers without a server, we'll use a different approach:
        // The room host will use a predictable peer ID based on the room code
        const roomHostId = `chess_room_${roomCode}`;

        try {
            console.log('🔍 Attempting to connect to room host:', roomHostId);
            console.log('🔧 My peer ID:', this.peer.id);
            console.log('🔧 My peer status:', this.peer.open ? 'open' : 'not open');

            if (!this.peer.open) {
                throw new Error('My peer connection is not open');
            }

            const conn = this.peer.connect(roomHostId, {
                reliable: true,
                serialization: 'json'
            });

            if (conn) {
                console.log('📡 Connection object created, setting up handlers...');
                this.remotePeerId = roomHostId;

                // Add immediate error handler
                conn.on('error', (error) => {
                    console.error('💥 Connection error:', error);
                    this.hideConnectionProgress();
                    if (error.type === 'peer-unavailable') {
                        this.showError('Room not found. Please check the room code or make sure the host is still online.');
                    } else {
                        this.showError('Connection failed: ' + error.message);
                    }
                });

                this.handleOutgoingConnection(conn);

                // Set a timeout to handle connection failure
                const connectionTimeout = setTimeout(() => {
                    if (!this.isConnected) {
                        console.log('⏰ Connection timeout reached');
                        this.hideConnectionProgress();
                        this.showError('Connection timeout. The room may not exist or the host may have disconnected.');
                    }
                }, 15000); // 15 second timeout

                // Clear timeout if connection succeeds
                conn.on('open', () => {
                    console.log('🎉 Connection opened successfully!');
                    clearTimeout(connectionTimeout);
                });

            } else {
                console.log('❌ Failed to create connection object');
                this.hideConnectionProgress();
                this.showError('Failed to create connection. Please try again.');
            }
        } catch (error) {
            console.error('💥 Error connecting to room:', error);
            this.hideConnectionProgress();
            this.showError('Failed to connect to room: ' + error.message);
        }
    }

    handleIncomingConnection(conn) {
        this.connection = conn;
        this.remotePeerId = conn.peer;
        this.setupConnectionHandlers(conn);
        
        // Host assigns colors: host is white, guest is black
        this.playerSide = 'w';
        this.isPlayerTurn = true;
        
        console.log('Incoming connection established');
    }

    handleOutgoingConnection(conn) {
        this.connection = conn;
        this.setupConnectionHandlers(conn);
        
        // Guest gets black pieces
        this.playerSide = 'b';
        this.isPlayerTurn = false;
        
        console.log('Outgoing connection initiated');
    }

    setupConnectionHandlers(conn) {
        conn.on('open', () => {
            console.log('Connection opened with:', conn.peer);
            this.isConnected = true;
            this.hideConnectionProgress();
            this.startGame();
        });

        conn.on('data', (data) => {
            this.handleMessage(data);
        });

        conn.on('close', () => {
            console.log('Connection closed');
            this.handleDisconnection();
        });

        conn.on('error', (error) => {
            console.error('Connection error:', error);
            this.handleConnectionError(error);
        });
    }

    handleMessage(data) {
        console.log('Received message:', data);
        
        switch (data.type) {
            case 'move':
                this.handleOpponentMove(data);
                break;
            case 'gameStart':
                this.handleGameStart(data);
                break;
            case 'resignation':
                this.handleOpponentResignation();
                break;
            case 'timeUpdate':
                this.handleTimeUpdate(data);
                break;
            default:
                console.log('Unknown message type:', data.type);
        }
    }

    sendMessage(data) {
        if (this.connection && this.connection.open) {
            this.connection.send(data);
            console.log('Sent message:', data);
        } else {
            console.error('Cannot send message: connection not open');
        }
    }

    handleOpponentMove(data) {
        // This will be called by the main chess game
        if (window.handleP2POpponentMove) {
            window.handleP2POpponentMove(data);
        }
    }

    handleGameStart(data) {
        console.log('Game start message received:', data);
    }

    handleOpponentResignation() {
        if (window.handleP2POpponentResignation) {
            window.handleP2POpponentResignation();
        }
    }

    handleTimeUpdate(data) {
        if (window.handleP2PTimeUpdate) {
            window.handleP2PTimeUpdate(data);
        }
    }

    startGame() {
        this.gameStarted = true;
        this.p2pLobby.style.display = 'none';
        this.gameMode.style.display = 'block';
        
        // Notify the main game to start
        if (window.startP2PGame) {
            window.startP2PGame(this.playerSide, this.isPlayerTurn);
        }
        
        console.log('Game started - Player side:', this.playerSide, 'Is my turn:', this.isPlayerTurn);
    }

    // Public methods for the main game to use
    sendMove(moveData) {
        this.sendMessage({
            type: 'move',
            move: moveData.move,
            fen: moveData.fen
        });
    }

    sendResignation() {
        this.sendMessage({
            type: 'resignation'
        });
    }

    sendTimeUpdate(timeData) {
        this.sendMessage({
            type: 'timeUpdate',
            whiteTime: timeData.whiteTime,
            blackTime: timeData.blackTime
        });
    }

    // UI helper methods
    updateStatus(text, status = 'disconnected') {
        this.statusText.textContent = text;
        const indicator = document.querySelector('.status-indicator');
        indicator.className = `fas fa-circle status-indicator ${status}`;
    }

    showConnectionProgress(message) {
        this.connectionMessage.textContent = message;
        this.connectionProgress.style.display = 'block';
    }

    hideConnectionProgress() {
        this.connectionProgress.style.display = 'none';
        this.createRoomBtn.textContent = 'Create Room';
        this.createRoomBtn.disabled = false;

        // Reset to main lobby view if we're in room creation view
        if (this.roomCreationView.style.display !== 'none') {
            this.backToLobby();
        }

        this.roomCode = null;
    }

    cancelConnection() {
        if (this.connection) {
            this.connection.close();
        }
        this.hideConnectionProgress();
        this.isHost = false;
        this.remotePeerId = null;
    }

    copyRoomCode() {
        if (this.roomCode) {
            navigator.clipboard.writeText(this.roomCode).then(() => {
                this.copyCodeBtn.innerHTML = '<i class="fas fa-check"></i>';
                setTimeout(() => {
                    this.copyCodeBtn.innerHTML = '<i class="fas fa-copy"></i>';
                }, 2000);
            }).catch(() => {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = this.roomCode;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);

                this.copyCodeBtn.innerHTML = '<i class="fas fa-check"></i>';
                setTimeout(() => {
                    this.copyCodeBtn.innerHTML = '<i class="fas fa-copy"></i>';
                }, 2000);
            });
        }
    }

    showError(message) {
        // Create a more user-friendly error display
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-notification';
        errorDiv.innerHTML = `
            <div class="error-content">
                <i class="fas fa-exclamation-triangle"></i>
                <span>${message}</span>
                <button class="error-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // Add styles if not already present
        if (!document.querySelector('#error-styles')) {
            const style = document.createElement('style');
            style.id = 'error-styles';
            style.textContent = `
                .error-notification {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background-color: #dc3545;
                    color: white;
                    padding: 15px;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                    z-index: 10000;
                    max-width: 400px;
                    animation: slideInRight 0.3s ease-out;
                }
                .error-content {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }
                .error-close {
                    background: none;
                    border: none;
                    color: white;
                    cursor: pointer;
                    padding: 5px;
                    margin-left: auto;
                }
                @keyframes slideInRight {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(errorDiv);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (errorDiv.parentElement) {
                errorDiv.remove();
            }
        }, 5000);
    }

    handleDisconnection() {
        this.isConnected = false;
        this.connection = null;
        this.updateStatus('Disconnected', 'disconnected');

        if (this.gameStarted) {
            this.showReconnectionDialog();
        }
    }

    showReconnectionDialog() {
        const reconnectDiv = document.createElement('div');
        reconnectDiv.className = 'reconnect-dialog';
        reconnectDiv.innerHTML = `
            <div class="reconnect-content">
                <h3>Connection Lost</h3>
                <p>The connection to your opponent was lost.</p>
                <div class="reconnect-actions">
                    <button id="try-reconnect" class="action-btn secondary">
                        <i class="fas fa-sync"></i>
                        Try Reconnect
                    </button>
                    <button id="return-lobby" class="action-btn cancel">
                        <i class="fas fa-home"></i>
                        Return to Lobby
                    </button>
                </div>
            </div>
        `;

        // Add styles if not already present
        if (!document.querySelector('#reconnect-styles')) {
            const style = document.createElement('style');
            style.id = 'reconnect-styles';
            style.textContent = `
                .reconnect-dialog {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0,0,0,0.7);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 10000;
                }
                .reconnect-content {
                    background: white;
                    padding: 30px;
                    border-radius: 10px;
                    text-align: center;
                    max-width: 400px;
                    margin: 20px;
                }
                .reconnect-content h3 {
                    margin-bottom: 15px;
                    color: #333;
                }
                .reconnect-content p {
                    margin-bottom: 25px;
                    color: #666;
                }
                .reconnect-actions {
                    display: flex;
                    gap: 15px;
                    justify-content: center;
                    flex-wrap: wrap;
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(reconnectDiv);

        // Handle button clicks
        document.getElementById('try-reconnect').addEventListener('click', () => {
            reconnectDiv.remove();
            this.attemptReconnection();
        });

        document.getElementById('return-lobby').addEventListener('click', () => {
            reconnectDiv.remove();
            if (window.handleP2PDisconnection) {
                window.handleP2PDisconnection();
            }
        });
    }

    attemptReconnection() {
        if (!this.remotePeerId) {
            this.showError('Cannot reconnect: no remote peer ID');
            return;
        }

        this.updateStatus('Reconnecting...', 'connecting');
        this.showConnectionProgress('Attempting to reconnect...');

        try {
            const conn = this.peer.connect(this.remotePeerId);
            this.handleOutgoingConnection(conn);

            // Set a timeout for reconnection attempt
            setTimeout(() => {
                if (!this.isConnected) {
                    this.hideConnectionProgress();
                    this.showError('Reconnection failed');
                    this.showReconnectionDialog();
                }
            }, 10000); // 10 second timeout

        } catch (error) {
            console.error('Reconnection failed:', error);
            this.hideConnectionProgress();
            this.showError('Reconnection failed: ' + error.message);
            this.showReconnectionDialog();
        }
    }

    handleConnectionError(error) {
        console.error('Connection error:', error);
        this.hideConnectionProgress();
        this.showError('Connection failed: ' + error.message);
    }

    // Cleanup method
    destroy() {
        if (this.connection) {
            this.connection.close();
        }
        if (this.peer) {
            this.peer.destroy();
        }
    }
}

// Global P2P manager instance
window.p2pManager = null;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.p2pManager = new P2PChessManager();
    window.p2pManager.initializePeer();
});
